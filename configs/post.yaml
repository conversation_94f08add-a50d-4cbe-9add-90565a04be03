# 业务指标配置示例

# Dashboard基础配置
name: "post"
refresh: "30s"
time_from: "now-1h"
time_to: "now"

# 数据源配置
datasource_uid: "fed52fm9xff9ca"

# 指标配置
metrics:
  - metric: "post_comment_at_cache"
    type: "counter"
    desc: "post_comment_at_cache"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_at_record"
    type: "counter"
    desc: "post_at_record"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "get_last_post_at_user"
    type: "counter"
    desc: "get_last_post_at_user"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_at_base_filter"
    type: "counter"
    desc: "post_at_base_filter"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_limit_access_get"
    type: "counter"
    desc: "post_limit_access_get"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_limit_publish"
    type: "counter"
    desc: "post_limit_publish"
    decimals: 1
    groupBy: ["instance", "method"]
    
  - metric: "post_limit_change_status"
    type: "counter"
    desc: "post_limit_change_status"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_limit_change_status_fail"
    type: "counter"
    desc: "post_limit_change_status_fail"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_boost_audit"
    type: "counter"
    desc: "post_boost_audit"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_boost_use"
    type: "counter"
    desc: "post_boost_use"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_exposure_rcmd_error"
    type: "counter"
    desc: "post_exposure_rcmd_error"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "post_boost_auto_expose_fail_with_post_not_normal"
    type: "counter"
    desc: "post_boost_auto_expose_fail_with_post_not_normal"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "popular_post_cache"
    type: "counter"
    desc: "popular_post_cache"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "popular_post_update_cache"
    type: "counter"
    desc: "popular_post_update_cache"
    decimals: 1
    groupBy: ["instance", "method"]