# 业务指标配置示例

# Dashboard基础配置
name: "弹窗"
refresh: "30s"
time_from: "now-1h"
time_to: "now"

# 数据源配置
datasource_uid: "fed52fm9xff9ca"

# 指标配置
metrics:
  - metric: "live_rpc_counter"
    type: "counter"
    desc: "收件箱请求"
    rowname: "请求指标"
    decimals: 1
    groupBy: ["instance", "method"]

# avatar_multi_window_show
# avatar_multi_rule_get_error
# record_avatar_multi_window_show
# record_avatar_multi_window_show_error
# check_avatar_multi_window_showed
# check_avatar_multi_window_showed_db
# check_avatar_multi_window_showed_cache
# check_avatar_multi_window_show_error
# mask_match_window_show_count
# filter_match_guide_window_show
  - metric: "avatar_multi_window_show"
    type: "counter"
    desc: "avatar_multi_window_show"
    decimals: 1
  
  - metric: "avatar_multi_rule_get_error"
    type: "counter"
    desc: "avatar_multi_rule_get_error"

    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "record_avatar_multi_window_show"
    type: "counter"
    desc: "record_avatar_multi_window_show"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "record_avatar_multi_window_show_error"
    type: "counter"
    desc: "record_avatar_multi_window_show_error"
    rowname: "record_avatar_multi_window_show_error"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "check_avatar_multi_window_showed"
    type: "counter"
    desc: "check_avatar_multi_window_showed"
    rowname: "check_avatar_multi_window_showed"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "check_avatar_multi_window_showed_db"
    type: "counter"
    desc: "check_avatar_multi_window_showed_db"
    rowname: "check_avatar_multi_window_showed_db"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "check_avatar_multi_window_showed_cache"
    type: "counter"
    desc: "check_avatar_multi_window_showed_cache"
    rowname: "check_avatar_multi_window_showed_cache"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "check_avatar_multi_window_show_error"
    type: "counter"
    desc: "check_avatar_multi_window_show_error"
    rowname: "check_avatar_multi_window_show_error"
    decimals: 1
    groupBy: ["instance", "method"]
    