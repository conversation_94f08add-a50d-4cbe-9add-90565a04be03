# 业务指标配置示例

# Dashboard基础配置
name: "dubbo 调用"
refresh: "30s"
time_from: "now-1h"
time_to: "now"

# 数据源配置
datasource_uid: "fed52fm9xff9ca"

# 指标配置
metrics:
  - metric: "live_rpc_counter"
    type: "counter"
    desc: "收件箱请求"
    rowname: "请求指标"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "dubbo_call"
    type: "timer"
    desc: "dubbo 调用"
    rowname: "性能指标"
    decimals: 2
    groupBy: ["instance", "method"]

  - metric: "post_at_record"
    type: "counter"
    desc: "post at"
    rowname: "post"
    decimals: 1

  - metric: "group_session_first_page_unread_check_counter"
    type: "counter"
    desc: "groupsession"
    rowname: "inbox"
    min_val: 0