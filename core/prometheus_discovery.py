#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prometheus指标自动发现器 - 核心模块
作者: yuangang
日期: 2025年7月

这个模块包含Prometheus指标发现的核心类和数据模型。
工具脚本请使用 scripts/prometheus_discovery_tool.py
"""

import requests
import re
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from urllib.parse import urljoin
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class DiscoveredMetric:
    """发现的指标信息"""
    name: str                    # 指标名称
    metric_type: str            # 指标类型 (counter, gauge, histogram, summary)
    help_text: str              # 帮助文本
    labels: Set[str]            # 标签名称集合
    desc_label: Optional[str] = None  # desc标签的值
    category: Optional[str] = None    # 指标分类 (从biz_前缀提取)

    def __post_init__(self):
        """后处理：提取分类信息"""
        if self.name.startswith('biz_'):
            # 提取分类，如 biz_inbox_request_total -> inbox
            parts = self.name.split('_')
            if len(parts) >= 2:
                self.category = parts[1]


@dataclass
class PrometheusTarget:
    """Prometheus目标信息"""
    job: str
    instance: str
    labels: Dict[str, str]


class PrometheusDiscovery:
    """Prometheus指标发现器"""

    def __init__(self, prometheus_url: str):
        """初始化发现器

        Args:
            prometheus_url: Prometheus服务地址
        """
        self.prometheus_url = prometheus_url.rstrip('/')
        self.session = requests.Session()

    def discover_targets(self, namespace: Optional[str] = None,
                         job_pattern: Optional[str] = None) -> List[PrometheusTarget]:
        """发现Prometheus目标

        Args:
            namespace: K8s命名空间过滤
            job_pattern: Job名称模式过滤

        Returns:
            目标列表
        """
        try:
            # 查询所有活跃目标
            url = urljoin(self.prometheus_url, '/api/v1/targets')
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            data = response.json()
            if data['status'] != 'success':
                raise Exception(
                    f"Prometheus API错误: {data.get('error', 'Unknown error')}")

            targets = []
            for target_data in data['data']['activeTargets']:
                labels = target_data.get('labels', {})

                # 命名空间过滤
                if namespace and labels.get('__meta_kubernetes_namespace') != namespace:
                    continue

                # Job模式过滤
                job = labels.get('job', '')
                if job_pattern and not re.search(job_pattern, job):
                    continue

                target = PrometheusTarget(
                    job=job,
                    instance=labels.get('instance', ''),
                    labels=labels
                )
                targets.append(target)

            logger.info(f"发现 {len(targets)} 个目标")
            return targets

        except Exception as e:
            logger.error(f"发现目标失败: {e}")
            raise

    def get_metrics_from_target(self, target: PrometheusTarget) -> List[DiscoveredMetric]:
        """从目标获取指标信息

        Args:
            target: 目标信息

        Returns:
            发现的指标列表
        """
        try:
            # 构建metrics端点URL
            instance = target.instance
            if not instance.startswith('http'):
                instance = f"http://{instance}"

            # 通常metrics路径是 /metrics 或 /actuator/prometheus
            metrics_paths = ['/metrics', '/actuator/prometheus']

            for path in metrics_paths:
                try:
                    metrics_url = urljoin(instance, path)
                    response = self.session.get(metrics_url, timeout=10)

                    if response.status_code == 200:
                        return self._parse_metrics_response(response.text)

                except requests.RequestException:
                    continue

            logger.warning(f"无法从目标 {target.instance} 获取指标")
            return []

        except Exception as e:
            logger.error(f"获取目标指标失败 {target.instance}: {e}")
            return []

    def _parse_metrics_response(self, metrics_text: str) -> List[DiscoveredMetric]:
        """解析metrics响应文本

        Args:
            metrics_text: metrics响应文本

        Returns:
            解析出的指标列表
        """
        metrics = {}

        for line in metrics_text.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                # 处理注释行，提取HELP和TYPE信息
                if line.startswith('# HELP '):
                    parts = line[7:].split(' ', 1)
                    if len(parts) == 2:
                        metric_name, help_text = parts
                        if metric_name not in metrics:
                            metrics[metric_name] = DiscoveredMetric(
                                name=metric_name,
                                metric_type='unknown',
                                help_text=help_text,
                                labels=set()
                            )
                        else:
                            metrics[metric_name].help_text = help_text

                elif line.startswith('# TYPE '):
                    parts = line[7:].split(' ', 1)
                    if len(parts) == 2:
                        metric_name, metric_type = parts
                        if metric_name not in metrics:
                            metrics[metric_name] = DiscoveredMetric(
                                name=metric_name,
                                metric_type=metric_type,
                                help_text='',
                                labels=set()
                            )
                        else:
                            metrics[metric_name].metric_type = metric_type
                continue

            # 解析指标行
            if ' ' in line:
                metric_part, value = line.rsplit(' ', 1)

                # 提取指标名和标签
                if '{' in metric_part and '}' in metric_part:
                    metric_name = metric_part[:metric_part.index('{')]
                    labels_part = metric_part[metric_part.index(
                        '{') + 1:metric_part.rindex('}')]

                    # 解析标签
                    labels = set()
                    desc_label = None

                    for label_pair in labels_part.split(','):
                        if '=' in label_pair:
                            label_name, label_value = label_pair.split('=', 1)
                            label_name = label_name.strip()
                            label_value = label_value.strip().strip('"')

                            labels.add(label_name)

                            # 提取desc标签
                            if label_name == 'desc':
                                desc_label = label_value
                else:
                    metric_name = metric_part
                    labels = set()
                    desc_label = None

                # 更新或创建指标
                if metric_name not in metrics:
                    metrics[metric_name] = DiscoveredMetric(
                        name=metric_name,
                        metric_type='unknown',
                        help_text='',
                        labels=labels
                    )
                else:
                    metrics[metric_name].labels.update(labels)

                # 设置desc标签
                if desc_label and not metrics[metric_name].desc_label:
                    metrics[metric_name].desc_label = desc_label

        return list(metrics.values())

    def discover_biz_metrics(self, namespace: Optional[str] = None,
                             job_pattern: Optional[str] = None) -> Dict[str, List[DiscoveredMetric]]:
        """发现业务指标（biz_前缀）

        Args:
            namespace: K8s命名空间过滤
            job_pattern: Job名称模式过滤

        Returns:
            按分类分组的指标字典
        """
        try:
            # 发现目标
            targets = self.discover_targets(namespace, job_pattern)

            all_metrics = []
            for target in targets:
                metrics = self.get_metrics_from_target(target)
                all_metrics.extend(metrics)

            # 过滤biz_前缀的指标
            biz_metrics = [m for m in all_metrics if m.name.startswith('biz_')]

            # 按分类分组
            categorized_metrics = {}
            for metric in biz_metrics:
                category = metric.category or 'common'
                if category not in categorized_metrics:
                    categorized_metrics[category] = []
                categorized_metrics[category].append(metric)

            logger.info(
                f"发现 {len(biz_metrics)} 个业务指标，分为 {len(categorized_metrics)} 个分类")
            return categorized_metrics

        except Exception as e:
            logger.error(f"发现业务指标失败: {e}")
            raise