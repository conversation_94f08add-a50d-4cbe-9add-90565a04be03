#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prometheus指标自动发现器 - 独立模块
作者: yuangang
日期: 2025年7月

这个模块可以独立运行，用于发现和分析Prometheus指标。
不依赖其他复杂组件，可以单独测试。
"""

import requests
import json
import re
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, asdict
from urllib.parse import urljoin
import logging
import argparse
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class DiscoveredMetric:
    """发现的指标信息"""
    name: str                    # 指标名称
    metric_type: str            # 指标类型 (counter, gauge, histogram, summary)
    help_text: str              # 帮助文本
    labels: Set[str]            # 标签名称集合
    desc_label: Optional[str] = None  # desc标签的值
    category: Optional[str] = None    # 指标分类 (从biz_前缀提取)

    def __post_init__(self):
        """后处理：提取分类信息"""
        if self.name.startswith('biz_'):
            # 提取分类，如 biz_inbox_request_total -> inbox
            parts = self.name.split('_')
            if len(parts) >= 2:
                self.category = parts[1]


@dataclass
class PrometheusTarget:
    """Prometheus目标信息"""
    job: str
    instance: str
    labels: Dict[str, str]


class PrometheusDiscovery:
    """Prometheus指标发现器"""

    def __init__(self, prometheus_url: str):
        """初始化发现器

        Args:
            prometheus_url: Prometheus服务地址
        """
        self.prometheus_url = prometheus_url.rstrip('/')
        self.session = requests.Session()

    def discover_targets(self, namespace: Optional[str] = None,
                         job_pattern: Optional[str] = None) -> List[PrometheusTarget]:
        """发现Prometheus目标

        Args:
            namespace: K8s命名空间过滤
            job_pattern: Job名称模式过滤

        Returns:
            目标列表
        """
        try:
            # 查询所有活跃目标
            url = urljoin(self.prometheus_url, '/api/v1/targets')
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            data = response.json()
            if data['status'] != 'success':
                raise Exception(
                    f"Prometheus API错误: {data.get('error', 'Unknown error')}")

            targets = []
            for target_data in data['data']['activeTargets']:
                labels = target_data.get('labels', {})

                # 命名空间过滤
                if namespace and labels.get('__meta_kubernetes_namespace') != namespace:
                    continue

                # Job模式过滤
                job = labels.get('job', '')
                if job_pattern and not re.search(job_pattern, job):
                    continue

                target = PrometheusTarget(
                    job=job,
                    instance=labels.get('instance', ''),
                    labels=labels
                )
                targets.append(target)

            logger.info(f"发现 {len(targets)} 个目标")
            return targets

        except Exception as e:
            logger.error(f"发现目标失败: {e}")
            raise

    def get_metrics_from_target(self, target: PrometheusTarget) -> List[DiscoveredMetric]:
        """从目标获取指标信息

        Args:
            target: 目标信息

        Returns:
            发现的指标列表
        """
        try:
            # 构建metrics端点URL
            instance = target.instance
            if not instance.startswith('http'):
                instance = f"http://{instance}"

            # 通常metrics路径是 /metrics 或 /actuator/prometheus
            metrics_paths = ['/metrics', '/actuator/prometheus']

            for path in metrics_paths:
                try:
                    metrics_url = urljoin(instance, path)
                    response = self.session.get(metrics_url, timeout=10)

                    if response.status_code == 200:
                        return self._parse_metrics_response(response.text)

                except requests.RequestException:
                    continue

            logger.warning(f"无法从目标 {target.instance} 获取指标")
            return []

        except Exception as e:
            logger.error(f"获取目标指标失败 {target.instance}: {e}")
            return []

    def _parse_metrics_response(self, metrics_text: str) -> List[DiscoveredMetric]:
        """解析metrics响应文本

        Args:
            metrics_text: metrics响应文本

        Returns:
            解析出的指标列表
        """
        metrics = {}

        for line in metrics_text.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                # 处理注释行，提取HELP和TYPE信息
                if line.startswith('# HELP '):
                    parts = line[7:].split(' ', 1)
                    if len(parts) == 2:
                        metric_name, help_text = parts
                        if metric_name not in metrics:
                            metrics[metric_name] = DiscoveredMetric(
                                name=metric_name,
                                metric_type='unknown',
                                help_text=help_text,
                                labels=set()
                            )
                        else:
                            metrics[metric_name].help_text = help_text

                elif line.startswith('# TYPE '):
                    parts = line[7:].split(' ', 1)
                    if len(parts) == 2:
                        metric_name, metric_type = parts
                        if metric_name not in metrics:
                            metrics[metric_name] = DiscoveredMetric(
                                name=metric_name,
                                metric_type=metric_type,
                                help_text='',
                                labels=set()
                            )
                        else:
                            metrics[metric_name].metric_type = metric_type
                continue

            # 解析指标行
            if ' ' in line:
                metric_part, value = line.rsplit(' ', 1)

                # 提取指标名和标签
                if '{' in metric_part and '}' in metric_part:
                    metric_name = metric_part[:metric_part.index('{')]
                    labels_part = metric_part[metric_part.index(
                        '{') + 1:metric_part.rindex('}')]

                    # 解析标签
                    labels = set()
                    desc_label = None

                    for label_pair in labels_part.split(','):
                        if '=' in label_pair:
                            label_name, label_value = label_pair.split('=', 1)
                            label_name = label_name.strip()
                            label_value = label_value.strip().strip('"')

                            labels.add(label_name)

                            # 提取desc标签
                            if label_name == 'desc':
                                desc_label = label_value
                else:
                    metric_name = metric_part
                    labels = set()
                    desc_label = None

                # 更新或创建指标
                if metric_name not in metrics:
                    metrics[metric_name] = DiscoveredMetric(
                        name=metric_name,
                        metric_type='unknown',
                        help_text='',
                        labels=labels
                    )
                else:
                    metrics[metric_name].labels.update(labels)

                # 设置desc标签
                if desc_label and not metrics[metric_name].desc_label:
                    metrics[metric_name].desc_label = desc_label

        return list(metrics.values())

    def discover_biz_metrics(self, namespace: Optional[str] = None,
                             job_pattern: Optional[str] = None) -> Dict[str, List[DiscoveredMetric]]:
        """发现业务指标（biz_前缀）

        Args:
            namespace: K8s命名空间过滤
            job_pattern: Job名称模式过滤

        Returns:
            按分类分组的指标字典
        """
        try:
            # 发现目标
            targets = self.discover_targets(namespace, job_pattern)

            all_metrics = []
            for target in targets:
                metrics = self.get_metrics_from_target(target)
                all_metrics.extend(metrics)

            # 过滤biz_前缀的指标
            biz_metrics = [m for m in all_metrics if m.name.startswith('biz_')]

            # 按分类分组
            categorized_metrics = {}
            for metric in biz_metrics:
                category = metric.category or 'common'
                if category not in categorized_metrics:
                    categorized_metrics[category] = []
                categorized_metrics[category].append(metric)

            logger.info(
                f"发现 {len(biz_metrics)} 个业务指标，分为 {len(categorized_metrics)} 个分类")
            return categorized_metrics

        except Exception as e:
            logger.error(f"发现业务指标失败: {e}")
            raise


def test_connection(prometheus_url: str) -> bool:
    """测试Prometheus连接"""
    try:
        discovery = PrometheusDiscovery(prometheus_url)
        targets = discovery.discover_targets()
        print(f"✅ 连接成功，发现 {len(targets)} 个目标")
        return True
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


def list_targets(prometheus_url: str, namespace: Optional[str] = None,
                 job_pattern: Optional[str] = None) -> List[PrometheusTarget]:
    """列出所有目标"""
    discovery = PrometheusDiscovery(prometheus_url)
    targets = discovery.discover_targets(namespace, job_pattern)

    print(f"发现的目标 ({len(targets)} 个):")
    print("-" * 60)
    for i, target in enumerate(targets, 1):
        print(f"{i:2d}. Job: {target.job}")
        print(f"    Instance: {target.instance}")
        if 'namespace' in target.labels:
            print(f"    Namespace: {target.labels['namespace']}")
        print()

    return targets


def discover_metrics_from_target(prometheus_url: str, target_instance: str) -> List[DiscoveredMetric]:
    """从指定目标发现指标"""
    discovery = PrometheusDiscovery(prometheus_url)

    # 创建临时目标对象
    target = PrometheusTarget(
        job="manual",
        instance=target_instance,
        labels={}
    )

    metrics = discovery.get_metrics_from_target(target)

    print(f"从目标 {target_instance} 发现的指标:")
    print("-" * 60)

    # 按类型分组
    by_type = {}
    for metric in metrics:
        if metric.metric_type not in by_type:
            by_type[metric.metric_type] = []
        by_type[metric.metric_type].append(metric)

    for metric_type, metric_list in by_type.items():
        print(f"\n{metric_type.upper()} 类型 ({len(metric_list)} 个):")
        for metric in metric_list[:5]:  # 只显示前5个
            print(f"  - {metric.name}")
            if metric.desc_label:
                print(f"    描述: {metric.desc_label}")
        if len(metric_list) > 5:
            print(f"  ... 还有 {len(metric_list) - 5} 个")

    return metrics


def discover_biz_metrics(prometheus_url: str, namespace: Optional[str] = None,
                         job_pattern: Optional[str] = None,
                         save_to_file: Optional[str] = None) -> Dict[str, List[DiscoveredMetric]]:
    """发现业务指标"""
    discovery = PrometheusDiscovery(prometheus_url)
    metrics = discovery.discover_biz_metrics(namespace, job_pattern)

    print(f"发现的业务指标分类:")
    print("=" * 60)

    total_metrics = 0
    for category, metric_list in metrics.items():
        total_metrics += len(metric_list)
        print(f"\n📋 分类: {category} ({len(metric_list)} 个指标)")
        print("-" * 40)

        for metric in metric_list:
            print(f"  📊 {metric.name} ({metric.metric_type})")
            if metric.desc_label:
                print(f"      💬 {metric.desc_label}")
            if metric.help_text and metric.help_text != metric.desc_label:
                print(f"      📝 {metric.help_text}")
            if metric.labels:
                print(f"      🏷️  标签: {', '.join(sorted(metric.labels))}")
            print()

    print(f"📈 总计: {total_metrics} 个业务指标，{len(metrics)} 个分类")

    # 保存到文件
    if save_to_file:
        import json
        output_data = {}
        for category, metric_list in metrics.items():
            output_data[category] = [asdict(metric) for metric in metric_list]

        with open(save_to_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        print(f"💾 结果已保存到: {save_to_file}")

    return metrics


def main():
    """主函数 - 提供多种测试模式"""
    parser = argparse.ArgumentParser(
        description='Prometheus指标发现器 - 独立测试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 测试连接
  python3 prometheus_discovery.py test --prometheus-url http://localhost:9090

  # 列出目标
  python3 prometheus_discovery.py targets --prometheus-url http://localhost:9090

  # 从指定目标发现指标
  python3 prometheus_discovery.py metrics --prometheus-url http://localhost:9090 --target http://localhost:8080/metrics

  # 发现业务指标
  python3 prometheus_discovery.py biz --prometheus-url http://localhost:9090 --namespace default
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # test命令
    test_parser = subparsers.add_parser('test', help='测试Prometheus连接')
    test_parser.add_argument(
        '--prometheus-url', required=True, help='Prometheus服务地址')

    # targets命令
    targets_parser = subparsers.add_parser('targets', help='列出所有目标')
    targets_parser.add_argument('--prometheus-url', required=True, help='Prometheus服务地址')
    targets_parser.add_argument('--namespace', help='K8s命名空间过滤')
    targets_parser.add_argument('--job-pattern', help='Job名称模式过滤')

    # metrics命令
    metrics_parser = subparsers.add_parser('metrics', help='从指定目标发现指标')
    metrics_parser.add_argument('--prometheus-url', required=True, help='Prometheus服务地址')
    metrics_parser.add_argument('--target', required=True, help='目标实例地址')

    # biz命令
    biz_parser = subparsers.add_parser('biz', help='发现业务指标')
    biz_parser.add_argument('--prometheus-url', required=True, help='Prometheus服务地址')
    biz_parser.add_argument('--namespace', help='K8s命名空间过滤')
    biz_parser.add_argument('--job-pattern', help='Job名称模式过滤')
    biz_parser.add_argument('--save', help='保存结果到文件')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    try:
        if args.command == 'test':
            success = test_connection(args.prometheus_url)
            return 0 if success else 1

        elif args.command == 'targets':
            list_targets(args.prometheus_url, args.namespace, args.job_pattern)
            return 0

        elif args.command == 'metrics':
            discover_metrics_from_target(args.prometheus_url, args.target)
            return 0

        elif args.command == 'biz':
            discover_biz_metrics(args.prometheus_url, args.namespace,
                                 args.job_pattern, args.save)
            return 0

    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
