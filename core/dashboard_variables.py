#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard变量统一管理
作者: yuangang
日期: 2025年7月
"""

from grafana_foundation_sdk.builders.dashboard import QueryVariable, CustomVariable, ConstantVariable
from grafana_foundation_sdk.models.dashboard import DataSourceRef, VariableRefresh, VariableSort, VariableHide


class VariableFactory:
    """Dashboard变量工厂"""

    @staticmethod
    def create_namespace_variable(datasource_uid: str):
        """创建namespace变量 - 完整配置"""
        return (
            QueryVariable("namespace")
            .label("Namespace")
            .description("选择要监控的命名空间")
            .query("label_values(jvm_classes_loaded_classes,namespace)")
            .refresh(VariableRefresh.ON_TIME_RANGE_CHANGED)
            .datasource(DataSourceRef(type_val="prometheus", uid=datasource_uid))
            .sort(VariableSort.ALPHABETICAL_ASC)
            .multi(False)  # 不允许多选
            .include_all(False)  # 不包含"All"选项
            .hide(VariableHide.DONT_HIDE)  # 显示变量
        )

    @staticmethod
    def create_application_variable(datasource_uid: str):
        """创建application变量 - 完整配置"""
        return (
            QueryVariable("app")
            .label("app")
            .description("选择要监控的应用程序")
            .query('label_values(jvm_memory_used_bytes{namespace="$namespace"}, app)')
            .refresh(VariableRefresh.ON_TIME_RANGE_CHANGED)
            .datasource(DataSourceRef(type_val="prometheus", uid=datasource_uid))
            .sort(VariableSort.ALPHABETICAL_ASC)
            .multi(True)  # 允许多选应用
            .include_all(True)  # 包含"All"选项
            .all_value(".*")  # All选项的值
            .hide(VariableHide.DONT_HIDE)
        )

    @staticmethod
    def create_instance_variable(datasource_uid: str):
        """创建instance变量 - 完整配置"""
        return (
            QueryVariable("instance")
            .label("instance")
            .description("选择要监控的实例")
            .query('label_values(jvm_memory_used_bytes{namespace="$namespace",app="$app"}, instance)')
            .refresh(VariableRefresh.ON_TIME_RANGE_CHANGED)
            .datasource(DataSourceRef(type_val="prometheus", uid=datasource_uid))
            .sort(VariableSort.ALPHABETICAL_ASC)
            .multi(True)  # 允许多选实例
            .include_all(True)  # 包含"All"选项
            .all_value(".*")  # All选项的值
            .hide(VariableHide.DONT_HIDE)
            .regex("")  # 可以用正则过滤结果
        )

    @staticmethod
    def create_custom_variable(name: str, label: str, options: list, description: str = ""):
        """创建自定义变量"""
        return (
            CustomVariable(name)
            .label(label)
            .description(description)
            .options(options)
            .multi(False)
            .include_all(False)
            .hide(VariableHide.DONT_HIDE)
        )

    @staticmethod
    def create_constant_variable(name: str, value: str, description: str = ""):
        """创建常量变量"""
        return (
            ConstantVariable(name)
            .description(description)
            .value(value)
            .hide(VariableHide.VARIABLE)  # 隐藏常量变量
        )
 
    @staticmethod
    def create_pod_variable(datasource_uid: str):
        """创建pod变量"""
        return (
            QueryVariable("pod")
            .label("Pod")
            .query('label_values(kube_pod_info{namespace="$namespace"}, pod)')
            .refresh(VariableRefresh.ON_TIME_RANGE_CHANGED)
            .datasource(DataSourceRef(type_val="prometheus", uid=datasource_uid))
            .sort(1)
        )

class DashboardVariables:
    """预定义的Dashboard变量组合"""

    @staticmethod
    def service_metrics_variables(datasource_uid: str):
        """服务监控Dashboard变量"""
        return [
            VariableFactory.create_namespace_variable(datasource_uid),
            VariableFactory.create_application_variable(datasource_uid),
            VariableFactory.create_instance_variable(datasource_uid)
        ]

    @staticmethod
    def kubernetes_variables(datasource_uid: str):
        """Kubernetes监控Dashboard变量"""
        return [
            VariableFactory.create_namespace_variable(datasource_uid),
            VariableFactory.create_service_variable(datasource_uid),
            VariableFactory.create_pod_variable(datasource_uid)
        ]

    @staticmethod
    def database_variables(datasource_uid: str):
        """数据库监控Dashboard变量"""
        return [
            VariableFactory.create_namespace_variable(datasource_uid),
            VariableFactory.create_application_variable(datasource_uid),
            VariableFactory.create_instance_variable(datasource_uid),
        ]

    @staticmethod
    def microservice_variables(datasource_uid: str):
        """微服务监控Dashboard变量"""
        return [
            VariableFactory.create_namespace_variable(datasource_uid),
            VariableFactory.create_service_variable(datasource_uid),
            VariableFactory.create_application_variable(datasource_uid),
            VariableFactory.create_instance_variable(datasource_uid)
        ]

    @staticmethod
    def infrastructure_variables(datasource_uid: str):
        """基础设施监控Dashboard变量"""
        return [
            VariableFactory.create_namespace_variable(datasource_uid),
            VariableFactory.create_job_variable(datasource_uid),
            VariableFactory.create_instance_variable(datasource_uid)
        ]

    @staticmethod
    def custom_variables(datasource_uid: str, variable_configs: list):
        """自定义变量组合"""
        variables = []
        for config in variable_configs:
            var_type = config.get("type", "query")
            if var_type == "query":
                variables.append(
                    QueryVariable(config["name"])
                    .label(config.get("label", config["name"]))
                    .query(config["query"])
                    .refresh(VariableRefresh.ON_TIME_RANGE_CHANGED)
                    .datasource(DataSourceRef(type_val="prometheus", uid=datasource_uid))
                    .sort(config.get("sort", 1))
                )
        return variables


# 便捷函数
def get_variables_for_dashboard_type(dashboard_type: str, datasource_uid: str):
    """根据dashboard类型获取对应的变量"""
    variable_map = {
        "service": DashboardVariables.service_metrics_variables,
        "kubernetes": DashboardVariables.kubernetes_variables,
        "database": DashboardVariables.database_variables,
        "microservice": DashboardVariables.microservice_variables,
        "infrastructure": DashboardVariables.infrastructure_variables
    }

    if dashboard_type in variable_map:
        return variable_map[dashboard_type](datasource_uid)
    else:
        # 默认返回服务监控变量
        return DashboardVariables.service_metrics_variables(datasource_uid)
