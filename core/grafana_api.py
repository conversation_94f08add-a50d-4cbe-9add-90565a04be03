#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Grafana API集成模块
作者: yuangang
日期: 2025年7月
"""

import requests
import json
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class GrafanaFolder:
    """Grafana文件夹信息"""
    id: int
    uid: str
    title: str
    url: str
    parent_uid: Optional[str] = None


@dataclass
class GrafanaDashboard:
    """Grafana Dashboard信息"""
    id: int
    uid: str
    title: str
    url: str
    folder_id: int
    folder_title: str
    folderUid: Optional[str] = None


class GrafanaAPI:
    """Grafana API客户端"""
    
    def __init__(self, base_url: str, api_key: str):
        """初始化API客户端
        
        Args:
            base_url: Grafana服务地址
            api_key: API密钥
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接
        
        Returns:
            (是否成功, 消息)
        """
        try:
            response = self.session.get(f"{self.base_url}/api/health")
            if response.status_code == 200:
                return True, "连接成功"
            else:
                return False, f"连接失败: HTTP {response.status_code}"
        except Exception as e:
            return False, f"连接失败: {e}"
    
    def create_folder(self, title: str, uid: Optional[str] = None) -> Tuple[bool, str, Optional[GrafanaFolder]]:
        """创建文件夹
        
        Args:
            title: 文件夹标题
            uid: 文件夹UID（可选）
            
        Returns:
            (是否成功, 消息, 文件夹信息)
        """
        try:
            data = {'title': title}
            if uid:
                data['uid'] = uid
            
            response = self.session.post(
                f"{self.base_url}/api/folders",
                json=data
            )
            
            if response.status_code == 200:
                folder_data = response.json()
                folder = GrafanaFolder(
                    id=folder_data['id'],
                    uid=folder_data['uid'],
                    title=folder_data['title'],
                    url=folder_data['url']
                )
                return True, "文件夹创建成功", folder
            elif response.status_code == 409:
                return False, "文件夹已存在", None
            else:
                error_msg = response.json().get('message', f'HTTP {response.status_code}')
                return False, f"创建文件夹失败: {error_msg}", None
                
        except Exception as e:
            return False, f"创建文件夹失败: {e}", None
    
    def get_folder_by_uid(self, uid: str) -> Optional[GrafanaFolder]:
        """根据UID获取文件夹
        
        Args:
            uid: 文件夹UID
            
        Returns:
            文件夹信息，如果不存在返回None
        """
        try:
            response = self.session.get(f"{self.base_url}/api/folders/{uid}")
            if response.status_code == 200:
                folder_data = response.json()
                return GrafanaFolder(
                    id=folder_data['id'],
                    uid=folder_data['uid'],
                    title=folder_data['title'],
                    url=folder_data['url']
                )
            return None
        except Exception as e:
            logger.error(f"获取文件夹失败: {e}")
            return None

    def get_folder_id_by_uid(self, uid: str) -> Optional[int]:
        """根据UID获取文件夹ID"""
        folder = self.get_folder(uid)
        if folder:
            return folder.id
        return None
    
    def list_folders(self) -> Optional[List[GrafanaFolder]]:
        """使用/api/search获取所有文件夹的扁平列表。"""
        try:
            # 使用search API获取所有文件夹，它会包含父文件夹信息
            response = self.session.get(f"{self.base_url}/api/search?type=dash-folder&limit=5000")
            if response.status_code != 200:
                logger.error(f"无法获取文件夹列表: HTTP {response.status_code} - {response.text}")
                return None

            folders_data = response.json()
            return [
                GrafanaFolder(
                    id=f['id'],
                    uid=f['uid'],
                    title=f['title'],
                    url=f.get('url', ''),
                    # 'folderUid' 字段表示父文件夹的UID
                    parent_uid=f.get('folderUid') 
                )
                for f in folders_data
            ]
        except Exception as e:
            logger.error(f"列出文件夹失败: {e}")
            return []
    
    def create_or_update_dashboard(self, dashboard_json: str, 
                                 folder_uid: Optional[str] = None,
                                 overwrite: bool = True) -> Tuple[bool, str, Optional[str]]:
        """创建或更新Dashboard
        
        Args:
            dashboard_json: Dashboard JSON内容
            folder_uid: 目标文件夹UID
            overwrite: 是否覆盖现有Dashboard
            
        Returns:
            (是否成功, 消息, Dashboard URL)
        """
        try:
            dashboard_data = json.loads(dashboard_json)
            
            # 构建请求数据
            request_data = {
                'dashboard': dashboard_data['dashboard'],
                'overwrite': overwrite
            }
            
            # 设置文件夹
            if folder_uid:
                folder = self.get_folder_by_uid(folder_uid)
                if folder:
                    request_data['folderId'] = folder.id
                else:
                    return False, f"文件夹不存在: {folder_uid}", None
            
            response = self.session.post(
                f"{self.base_url}/api/dashboards/db",
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.base_url}{result['url']}"
                return True, "Dashboard部署成功", dashboard_url
            else:
                error_msg = response.json().get('message', f'HTTP {response.status_code}')
                return False, f"部署Dashboard失败: {error_msg}", None
                
        except json.JSONDecodeError as e:
            return False, f"Dashboard JSON格式错误: {e}", None
        except Exception as e:
            return False, f"部署Dashboard失败: {e}", None
    
    def get_dashboard_by_uid(self, uid: str) -> Optional[Dict]:
        """根据UID获取Dashboard
        
        Args:
            uid: Dashboard UID
            
        Returns:
            Dashboard信息，如果不存在返回None
        """
        try:
            response = self.session.get(f"{self.base_url}/api/dashboards/uid/{uid}")
            if response.status_code == 200:
                data = response.json()
                dashboard_data = data['dashboard']
                meta = data['meta']
                
                return data
            return None
        except Exception as e:
            logger.error(f"获取Dashboard失败: {e}")
            return None
    
    def delete_dashboard_by_uid(self, uid: str) -> Tuple[bool, str]:
        """根据UID删除Dashboard
        
        Args:
            uid: Dashboard UID
            
        Returns:
            (是否成功, 消息)
        """
        try:
            response = self.session.delete(f"{self.base_url}/api/dashboards/uid/{uid}")
            if response.status_code == 200:
                return True, "Dashboard删除成功"
            else:
                error_msg = response.json().get('message', f'HTTP {response.status_code}')
                return False, f"删除Dashboard失败: {error_msg}"
        except Exception as e:
            return False, f"删除Dashboard失败: {e}"
    
    def search_dashboards(self, query: Optional[str] = None, 
                         folder_id: Optional[int] = None) -> List[GrafanaDashboard]:
        """搜索Dashboard
        
        Args:
            query: 搜索关键词
            folder_id: 文件夹ID过滤
            
        Returns:
            Dashboard列表
        """
        try:
            params = {'type': 'dash-db'}
            if query:
                params['query'] = query
            if folder_id is not None:
                params['folderIds'] = folder_id
            
            response = self.session.get(f"{self.base_url}/api/search", params=params)
            if response.status_code == 200:
                dashboards = []
                for item in response.json():
                    dashboard = GrafanaDashboard(
                        id=item['id'],
                        uid=item['uid'],
                        title=item['title'],
                        url=item['url'],
                        folder_id=item['folderId'],
                        folderUid=item.get('folderUid'),
                        folder_title=item['folderTitle']
                    )
                    dashboards.append(dashboard)
                return dashboards
            return []
        except Exception as e:
            logger.error(f"搜索Dashboard失败: {e}")
            return []


def main():
    """测试函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Grafana API测试')
    parser.add_argument('--url', required=True, help='Grafana服务地址')
    parser.add_argument('--api-key', required=True, help='API密钥')
    parser.add_argument('--action', choices=['test', 'folders', 'dashboards'], 
                       default='test', help='操作类型')
    
    args = parser.parse_args()
    
    api = GrafanaAPI(args.url, args.api_key)
    
    if args.action == 'test':
        success, message = api.test_connection()
        print(f"连接测试: {'成功' if success else '失败'} - {message}")
    
    elif args.action == 'folders':
        folders = api.list_folders()
        print(f"文件夹列表 ({len(folders)} 个):")
        for folder in folders:
            print(f"  - {folder.title} (UID: {folder.uid})")
    
    elif args.action == 'dashboards':
        dashboards = api.search_dashboards()
        print(f"Dashboard列表 ({len(dashboards)} 个):")
        for dashboard in dashboards:
            print(f"  - {dashboard.title} (UID: {dashboard.uid}) - 文件夹: {dashboard.folder_title}")
    
    return 0

if __name__ == "__main__":
    exit(main())
