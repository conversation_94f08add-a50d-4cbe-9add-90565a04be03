#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于指标配置的Dashboard构建器
作者: yuangang
日期: 2025年7月
"""

import os
import sys
from typing import List, Dict, Any

# 添加src目录到路径
current_dir = os.path.dirname(__file__)
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from grafana_foundation_sdk.builders.dashboard import Dashboard as DashboardBuilder, Row, TimePicker

try:
    from core.metric_config_models import MetricDashboardConfig, MetricConfig, MetricType, GeneratedPanelConfig
    from core.dashboard_variables import DashboardVariables
except ImportError:
    from metric_config_models import MetricDashboardConfig, MetricConfig, MetricType, GeneratedPanelConfig
    from dashboard_variables import DashboardVariables


class MetricDashboardBuilder:
    """基于指标配置的Dashboard构建器"""

    def __init__(self, config: MetricDashboardConfig):
        """初始化构建器"""
        self.config = config

    def generate_panels_for_metric(self, metric: MetricConfig) -> List[GeneratedPanelConfig]:
        """根据指标类型生成Panel配置"""
        panels = []
        if metric.type == MetricType.COUNTER:
            # Counter类型：生成QPS和3分钟数量两个panel
            panels.extend(self._generate_counter_panels(metric))
        elif metric.type == MetricType.GAUGE:
            # Gauge类型：生成单个panel
            panels.extend(self._generate_gauge_panels(metric))
        elif metric.type == MetricType.TIMER:
            # Timer类型：生成P95、P99、Max、QPS 四个panel
            panels.extend(self._generate_timer_panels(metric))
        return panels

    def _generate_counter_panels(self, metric: MetricConfig) -> List[GeneratedPanelConfig]:
        """生成Counter类型的panels"""
        panels = []

        # Counter类型：框架层面会生成 metricname_total
        metric_name_total = f"{metric.metric}_total"

        # 根据groupBy字段构建聚合表达式
        group_by_fields = metric.groupBy or ["instance"]
        group_by_clause = ", ".join(group_by_fields)
        sum_by_clause = f"sum by ({group_by_clause})"

        # Panel 1: QPS
        qps_panel = GeneratedPanelConfig(
            title=f"{metric.desc}(QPS)",
            expr=f"{sum_by_clause}(rate({metric_name_total}[1m]))",
            panel_type="timeseries",
            unit=metric.unit or "ops",
            min_val=metric.min_val or 0,
            max_val=metric.max_val,
            decimals=metric.decimals or 1,
            groupBy=group_by_fields
        )
        panels.append(qps_panel)

        # Panel 2: 3分钟数量
        count_panel = GeneratedPanelConfig(
            title=f"{metric.desc}(3分钟数量)",
            expr=f"{sum_by_clause}(increase({metric_name_total}[3m]))",
            panel_type="timeseries",
            unit=metric.unit or "short",
            min_val=metric.min_val or 0,
            max_val=metric.max_val,
            decimals=metric.decimals or 0,
            groupBy=group_by_fields
        )
        panels.append(count_panel)

        return panels

    def _generate_gauge_panels(self, metric: MetricConfig) -> List[GeneratedPanelConfig]:
        """生成Gauge类型的panels"""
        panels = []

        # Gauge类型：使用原值
        gauge_panel = GeneratedPanelConfig(
            title=metric.desc,
            expr=metric.metric,
            panel_type="gauge",
            unit=metric.unit or "short",
            min_val=metric.min_val,
            max_val=metric.max_val,
            decimals=metric.decimals or 1
        )
        panels.append(gauge_panel)

        return panels

    def _generate_timer_panels(self, metric: MetricConfig) -> List[GeneratedPanelConfig]:
        """生成Timer类型的panels"""
        panels = []

        # Timer类型：框架层面会生成五个指标
        # - metricname_seconds_bucket: 直方图桶
        # - metricname_seconds_count: 计数
        # - metricname_seconds_sum: 总和
        # - metricname_seconds_max: 最大值
        # - metricname_seconds (带 quantile 标签): 分位数指标

        base_name = metric.metric
        seconds_metric = f"{base_name}_seconds"
        count_metric = f"{base_name}_seconds_count"
        max_metric = f"{base_name}_seconds_max"

        # 根据groupBy字段构建聚合表达式
        group_by_fields = metric.groupBy or ["instance"]
        group_by_clause = ", ".join(group_by_fields)
        sum_by_clause = f"sum by ({group_by_clause})"

        # Panel 1: P95
        p95_panel = GeneratedPanelConfig(
            title=f"{metric.desc}(P95)",
            expr=f'{sum_by_clause}({seconds_metric}{{quantile="0.95"}})',
            panel_type="timeseries",
            unit=metric.unit or "s",
            min_val=metric.min_val or 0,
            max_val=metric.max_val,
            decimals=metric.decimals or 2,
            groupBy=group_by_fields
        )
        panels.append(p95_panel)

        # Panel 2: P99
        p99_panel = GeneratedPanelConfig(
            title=f"{metric.desc}(P99)",
            expr=f'{sum_by_clause}({seconds_metric}{{quantile="0.99"}})',
            panel_type="timeseries",
            unit=metric.unit or "s",
            min_val=metric.min_val or 0,
            max_val=metric.max_val,
            decimals=metric.decimals or 2,
            groupBy=group_by_fields
        )
        panels.append(p99_panel)

        # Panel 3: Max
        max_panel = GeneratedPanelConfig(
            title=f"{metric.desc}(Max)",
            expr=f'{sum_by_clause}({max_metric})',
            panel_type="timeseries",
            unit=metric.unit or "s",
            min_val=metric.min_val or 0,
            max_val=metric.max_val,
            decimals=metric.decimals or 3,
            groupBy=group_by_fields
        )
        panels.append(max_panel)

        # Panel 4: QPS (基于count指标)
        qps_panel = GeneratedPanelConfig(
            title=f"{metric.desc}(QPS)",
            expr=f"{sum_by_clause}(rate({count_metric}[1m]))",
            panel_type="timeseries",
            unit=metric.unit or "ops",
            min_val=metric.min_val or 0,
            max_val=metric.max_val,
            decimals=metric.decimals or 1,
            groupBy=group_by_fields
        )
        panels.append(qps_panel)

        return panels

    def build_dashboard_config(self) -> Any:
        """构建Dashboard对象"""
        dashboard_builder = DashboardBuilder(self.config.dashboard_title())

        # 添加变量
        variables = self._generate_variables()
        for variable in variables:
            dashboard_builder = dashboard_builder.with_variable(variable)

        # 添加其他Dashboard配置
        (dashboard_builder
         .tags(self.config.tags)
         .timezone("browser")
         .editable()
         .time(self.config.time_from, self.config.time_to)
         .timepicker(
            TimePicker().refresh_intervals([
                "5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"
            ])
        ))

        # 按行分组指标并创建面板
        rows_data = self.config.get_rows()

        for row_name, metrics in rows_data.items():
            current_group_panels = []
            for metric in metrics:
                generated_panels = self.generate_panels_for_metric(metric)
                current_group_panels.extend(generated_panels)

            if row_name is None:
                # 对于没有行名称的指标，直接将面板添加到顶层
                for panel_config in current_group_panels:
                    sdk_panel = panel_config.to_sdk_panel(self.config.datasource_uid)
                    dashboard_builder = dashboard_builder.with_panel(sdk_panel)
            else:
                # 对于有行名称的指标，创建行并添加面板
                row = Row(title=row_name)
                panels_in_row = []
                for panel_config in current_group_panels:
                    sdk_panel = panel_config.to_sdk_panel(self.config.datasource_uid)
                    panels_in_row.append(sdk_panel)
                row.panels(panels_in_row)
                dashboard_builder = dashboard_builder.with_panel(row)

        return dashboard_builder.build()

    def build_dashboard(self) -> Dict[str, Any]:
        """构建Grafana Dashboard对象"""
        return self.build_dashboard_config()

        # 创建Dashboard构建器
        dashboard_builder = (
            Dashboard(self.config.dashboard_title())
            .description("业务指标监控仪表盘")
            .tags(self.config.tags + ["auto-generated"])
            .refresh(self.config.refresh)
            .time(self.config.time_from, self.config.time_to)
        )



        # 按行分组指标并创建面板
        rows_data = self.config.get_rows()
        y_position = 0

        for row_name, metrics in rows_data.items():
            # 添加 row 类型的 panel
            row_panel = (
                Row(row_name)
                .grid_pos(GridPos(x=0, y=y_position, w=24, h=1))
                .collapsed(False)
            )
            dashboard_builder.with_row(row_panel)
            y_position += 1  # row panel 高度为 1

            # 为该行的所有指标生成面板
            x_position = 0
            row_height = 8  # 默认面板高度

            panel_creators = {
                "timeseries": self._create_timeseries_panel,
                "gauge": self._create_gauge_panel,
            }

            for metric in metrics:
                panel_configs = self.generate_panels_for_metric(metric)
                for panel_config in panel_configs:
                    creator = panel_creators.get(panel_config.panel_type)
                    if creator:
                        sdk_panel = creator(
                            panel_config=panel_config,
                            datasource=datasource,
                            x_position=x_position,
                            y_position=y_position,
                            row_height=row_height
                        )
                        # 将创建好的面板添加到当前行
                        row_panel.with_panel(sdk_panel)

                        # 更新下一个面板的水平位置
                        x_position += 12
                        if x_position >= 24:
                            x_position = 0
                            y_position += row_height

            # 移动到下一行
            if x_position > 0:
                y_position += row_height

        # 返回构建好的Dashboard对象
        return dashboard_builder.build()

    def _generate_variables(self) -> List[Any]:
        """生成Dashboard变量配置"""
        return DashboardVariables.service_metrics_variables(self.config.datasource_uid)

    def _create_timeseries_panel(self, panel_config, datasource, x_position, y_position, row_height):
        """创建一个时间序列面板。"""
        from grafana_foundation_sdk.builders.timeseries import Panel as Timeseries
        from grafana_foundation_sdk.builders.prometheus import Dataquery as PrometheusQuery
        from grafana_foundation_sdk.builders.common import VizLegendOptions
        from grafana_foundation_sdk.models.common import LegendDisplayMode, LegendPlacement
        from grafana_foundation_sdk.models.dashboard import GridPos

        # 根据groupBy字段动态生成图例格式
        legend_format = "-".join([f"{{{{{field}}}}}" for field in panel_config.groupBy]) if panel_config.groupBy else "{{instance}}"

        sdk_panel = (
            Timeseries()
            .title(panel_config.title)
            .grid_pos(GridPos(x=x_position, y=y_position, w=12, h=row_height))
            .datasource(datasource)
            .with_target(
                PrometheusQuery()
                .expr(panel_config.expr)
                .legend_format(legend_format)
            )
            .unit(panel_config.unit)
            .decimals(panel_config.decimals)
            .legend(
                VizLegendOptions()
                .display_mode(LegendDisplayMode.TABLE)
                .placement(LegendPlacement.BOTTOM)
                .calcs(["last","mean"])
                .show_legend(True)
            )
        )

        if panel_config.min_val is not None:
            sdk_panel.min(panel_config.min_val)
        if panel_config.max_val is not None:
            sdk_panel.max(panel_config.max_val)
        
        return sdk_panel

    def _create_gauge_panel(self, panel_config, datasource, x_position, y_position, row_height):
        """创建一个仪表盘面板。"""
        from grafana_foundation_sdk.builders.gauge import Panel as Gauge
        from grafana_foundation_sdk.builders.prometheus import Dataquery as PrometheusQuery
        from grafana_foundation_sdk.models.dashboard import GridPos

        sdk_panel = (
            Gauge()
            .title(panel_config.title)
            .grid_pos(GridPos(w=12, h=row_height))
            .datasource(datasource)
            .with_target(
                PrometheusQuery()
                .expr(panel_config.expr)
            )
            .unit(panel_config.unit)
            .decimals(panel_config.decimals)
        )

        return sdk_panel
