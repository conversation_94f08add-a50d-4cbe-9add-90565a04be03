#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于指标配置构建Grafana Dashboard的工具脚本
作者: yuangang
日期: 2025年7月
"""

import os
import sys
import yaml
import json
from grafana_foundation_sdk.cog.encoder import JSONEncoder
import argparse

# 添加项目根目录到路径，以便能够找到core模块
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from core.metric_dashboard_builder import MetricDashboardBuilder
from core.metric_config_models import MetricDashboardConfig


def load_config(config_file: str) -> MetricDashboardConfig:
    """加载指标配置文件"""
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    print(f"📄 加载配置文件: {config_file}")

    with open(config_file, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)

    return MetricDashboardConfig.from_dict(config_data)

def build_dashboard(config: MetricDashboardConfig, output_dir: str = None) -> str:
    """构建Dashboard并保存到文件"""
    print(f"🔨 开始构建Dashboard: {config.dashboard_title()}")

    # 创建构建器
    builder = MetricDashboardBuilder(config)

    # 构建Dashboard
    dashboard_obj = builder.build_dashboard()
    # 使用SDK的编码器将Dashboard对象编码，然后加载为字典
    encoder = JSONEncoder(sort_keys=True, indent=2)
    dashboard_dict = json.loads(encoder.encode(dashboard_obj))

    # 再次序列化为JSON字符串，确保中文不转义
    output_content = json.dumps(dashboard_dict, ensure_ascii=False, indent=2)

    # 确定输出目录
    if not output_dir:
        output_dir = os.path.join(project_root, 'output', 'dashboards')

    os.makedirs(output_dir, exist_ok=True)

    # 生成文件名
    dashboard_name = config.dashboard_title().lower().replace(' ',
                                                              '-').replace('_', '-')
    output_file = os.path.join(output_dir, f"{dashboard_name}.json")

    # 保存文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(output_content)

    return output_file


def show_dashboard_info(config: MetricDashboardConfig, output_file: str):
    """显示Dashboard信息"""
    rows = config.get_rows()

    print(f"\n✅ Dashboard构建成功!")
    print(f"📊 Dashboard: {config.dashboard_title()}")
    print(f"🆔 UID: {config.dashboard_uid()}")
    print(f"📁 输出文件: {output_file}")
    print(f"📈 总指标数量: {len(config.metrics)}")
    print(f"📋 行数量: {len(rows)}")
    print(f"🔄 刷新间隔: {config.refresh}")
    print(f"⏰ 时间范围: {config.time_from} ~ {config.time_to}")

    if config.tags:
        print(f"🏷️  标签: {', '.join(config.tags)}")

    print(f"\n📋 行详情:")
    for row_name, metrics in rows.items():
        print(f"  📂 {row_name}: {len(metrics)} 个指标")
        for metric in metrics:
            print(
                f"    - {metric.metric} ({metric.type.value}): {metric.desc}")


def validate_config(config_file: str):
    """验证配置文件"""
    try:
        config = load_config(config_file)
        print(f"✅ 配置文件验证通过")
        print(f"📊 Dashboard: {config.dashboard_title()}")
        print(f"📈 指标数量: {len(config.metrics)}")

        # 检查指标类型分布
        type_counts = {}
        for metric in config.metrics:
            type_name = metric.type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1

        print(f"📋 指标类型分布:")
        for metric_type, count in type_counts.items():
            print(f"  - {metric_type}: {count} 个")

        return True
    except Exception as e:
        print(f"❌ 配置文件验证失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='基于指标配置构建Grafana Dashboard',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 构建Dashboard
  python src/scripts/build_metric_dashboard.py build configs/metrics/biz_metrics_example.yaml
  
  # 构建并指定输出目录
  python src/scripts/build_metric_dashboard.py build configs/metrics/biz_metrics_example.yaml -o /tmp/dashboards
  
  # 验证配置文件
  python src/scripts/build_metric_dashboard.py validate configs/metrics/biz_metrics_example.yaml
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # build命令
    build_parser = subparsers.add_parser('build', help='构建Dashboard')
    build_parser.add_argument('config', help='指标配置文件路径')
    build_parser.add_argument('-o', '--output', help='输出目录')

    # validate命令
    validate_parser = subparsers.add_parser('validate', help='验证配置文件')
    validate_parser.add_argument('config', help='指标配置文件路径')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    try:
        if args.command == 'build':
            # 构建Dashboard
            config = load_config(args.config)
            output_file = build_dashboard(config, args.output)
            show_dashboard_info(config, output_file)
            return 0

        elif args.command == 'validate':
            # 验证配置文件
            if validate_config(args.config):
                return 0
            else:
                return 1

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
