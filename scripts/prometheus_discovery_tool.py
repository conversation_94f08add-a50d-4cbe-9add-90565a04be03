#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prometheus指标发现工具 - 独立脚本
作者: yuangang
日期: 2025年7月

这个工具脚本提供多种Prometheus指标发现和分析功能。
参考 backup_dashboards.py 的结构设计。
"""

import os
import sys
import json
import argparse
import logging
from typing import Dict, List, Optional
from dataclasses import asdict

# 添加项目根目录到路径，以便能够找到core模块
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# 直接导入prometheus_discovery模块，避免通过core包导入
sys.path.insert(0, os.path.join(project_root, 'core'))
from core.prometheus_discovery import PrometheusDiscovery, PrometheusTarget, DiscoveredMetric

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_connection(prometheus_url: str) -> bool:
    """测试Prometheus连接"""
    try:
        discovery = PrometheusDiscovery(prometheus_url)
        targets = discovery.discover_targets()
        print(f"✅ 连接成功，发现 {len(targets)} 个目标")
        return True
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


def list_targets(prometheus_url: str, namespace: Optional[str] = None,
                 job_pattern: Optional[str] = None) -> List[PrometheusTarget]:
    """列出所有目标"""
    discovery = PrometheusDiscovery(prometheus_url)
    targets = discovery.discover_targets(namespace, job_pattern)

    print(f"发现的目标 ({len(targets)} 个):")
    print("-" * 60)
    for i, target in enumerate(targets, 1):
        print(f"{i:2d}. Job: {target.job}")
        print(f"    Instance: {target.instance}")
        if 'namespace' in target.labels:
            print(f"    Namespace: {target.labels['namespace']}")
        print()

    return targets


def discover_metrics_from_target(prometheus_url: str, target_instance: str) -> List[DiscoveredMetric]:
    """从指定目标发现指标"""
    discovery = PrometheusDiscovery(prometheus_url)

    # 创建临时目标对象
    target = PrometheusTarget(
        job="manual",
        instance=target_instance,
        labels={}
    )

    metrics = discovery.get_metrics_from_target(target)

    print(f"从目标 {target_instance} 发现的指标:")
    print("-" * 60)

    # 按类型分组
    by_type = {}
    for metric in metrics:
        if metric.metric_type not in by_type:
            by_type[metric.metric_type] = []
        by_type[metric.metric_type].append(metric)

    for metric_type, metric_list in by_type.items():
        print(f"\n{metric_type.upper()} 类型 ({len(metric_list)} 个):")
        for metric in metric_list[:5]:  # 只显示前5个
            print(f"  - {metric.name}")
            if metric.desc_label:
                print(f"    描述: {metric.desc_label}")
        if len(metric_list) > 5:
            print(f"  ... 还有 {len(metric_list) - 5} 个")

    return metrics


def discover_biz_metrics(prometheus_url: str, namespace: Optional[str] = None,
                         job_pattern: Optional[str] = None,
                         save_to_file: Optional[str] = None) -> Dict[str, List[DiscoveredMetric]]:
    """发现业务指标"""
    discovery = PrometheusDiscovery(prometheus_url)
    metrics = discovery.discover_biz_metrics(namespace, job_pattern)

    print(f"发现的业务指标分类:")
    print("=" * 60)

    total_metrics = 0
    for category, metric_list in metrics.items():
        total_metrics += len(metric_list)
        print(f"\n📋 分类: {category} ({len(metric_list)} 个指标)")
        print("-" * 40)

        for metric in metric_list:
            print(f"  📊 {metric.name} ({metric.metric_type})")
            if metric.desc_label:
                print(f"      💬 {metric.desc_label}")
            if metric.help_text and metric.help_text != metric.desc_label:
                print(f"      📝 {metric.help_text}")
            if metric.labels:
                print(f"      🏷️  标签: {', '.join(sorted(metric.labels))}")
            print()

    print(f"📈 总计: {total_metrics} 个业务指标，{len(metrics)} 个分类")

    # 保存到文件
    if save_to_file:
        output_data = {}
        for category, metric_list in metrics.items():
            output_data[category] = [asdict(metric) for metric in metric_list]

        # 确保输出目录存在
        output_dir = os.path.dirname(save_to_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        with open(save_to_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        print(f"💾 结果已保存到: {save_to_file}")

    return metrics


def main():
    """主函数 - 提供多种测试模式"""
    parser = argparse.ArgumentParser(
        description='Prometheus指标发现工具 - 独立测试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 测试连接
  python3 prometheus_discovery_tool.py test --prometheus-url http://localhost:9090

  # 列出目标
  python3 prometheus_discovery_tool.py targets --prometheus-url http://localhost:9090

  # 从指定目标发现指标
  python3 prometheus_discovery_tool.py metrics --prometheus-url http://localhost:9090 --target http://localhost:8080/metrics

  # 发现业务指标
  python3 prometheus_discovery_tool.py biz --prometheus-url http://localhost:9090 --namespace default
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # test命令
    test_parser = subparsers.add_parser('test', help='测试Prometheus连接')
    test_parser.add_argument(
        '--prometheus-url', required=True, help='Prometheus服务地址')

    # targets命令
    targets_parser = subparsers.add_parser('targets', help='列出所有目标')
    targets_parser.add_argument(
        '--prometheus-url', required=True, help='Prometheus服务地址')
    targets_parser.add_argument('--namespace', help='K8s命名空间过滤')
    targets_parser.add_argument('--job-pattern', help='Job名称模式过滤')

    # metrics命令
    metrics_parser = subparsers.add_parser('metrics', help='从指定目标发现指标')
    metrics_parser.add_argument(
        '--prometheus-url', required=True, help='Prometheus服务地址')
    metrics_parser.add_argument('--target', required=True, help='目标实例地址')

    # biz命令
    biz_parser = subparsers.add_parser('biz', help='发现业务指标')
    biz_parser.add_argument(
        '--prometheus-url', required=True, help='Prometheus服务地址')
    biz_parser.add_argument('--namespace', help='K8s命名空间过滤')
    biz_parser.add_argument('--job-pattern', help='Job名称模式过滤')
    biz_parser.add_argument('--save', help='保存结果到文件')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    try:
        if args.command == 'test':
            success = test_connection(args.prometheus_url)
            return 0 if success else 1

        elif args.command == 'targets':
            list_targets(args.prometheus_url, args.namespace, args.job_pattern)
            return 0

        elif args.command == 'metrics':
            discover_metrics_from_target(args.prometheus_url, args.target)
            return 0

        elif args.command == 'biz':
            discover_biz_metrics(args.prometheus_url, args.namespace,
                                 args.job_pattern, args.save)
            return 0

    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
