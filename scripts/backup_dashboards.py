import datetime
import json
import logging
import os
import re
import shutil
import sys
import zipfile
from typing import Dict, List, Optional

import requests
from qcloud_cos import CosConfig, CosS3Client

# --- 动态添加项目根目录到 sys.path ---
# 这样做可以确保无论从哪里运行脚本，都能正确找到 core 模块
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from core.grafana_api import GrafanaAPI, GrafanaFolder

# --- 配置 ---
GRAFANA_URL = os.getenv('GRAFANA_URL', 'https://grafana.finkapp.cn')
COS_SECRET_ID = "AKIDufAPTHqAE4ex0nKaZHFzSebJlx0sYx2r"
COS_SECRET_KEY = "auxlJaaLq42jBNJyNWP2QYPzIL9gwwhV"
COS_REGION = 'ap-shanghai'
COS_BUCKET = 'finka-ops-1251485948'

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

def save_dashboard_locally(api_client: GrafanaAPI, uid: str, full_local_path: str):
    """获取并保存单个仪表盘的 JSON 定义到指定完整路径。"""
    try:
        dashboard_data = api_client.get_dashboard_by_uid(uid)
        if not dashboard_data:
            logging.error(f"获取仪表盘 '{uid}' 失败: 未找到或API错误")
            return None

        # 确保本地路径存在
        os.makedirs(full_local_path, exist_ok=True)

        # 仪表盘标题可能包含特殊字符，需要清理
        title = dashboard_data['dashboard']['title']
        sanitized_title = re.sub(r'[\\/:*?"<>|]', '', title)  # 移除文件名不允许的字符
        file_name = f"{sanitized_title}.json"
        file_path = os.path.join(full_local_path, file_name)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(dashboard_data, f, ensure_ascii=False, indent=2)
        logging.info(f"仪表盘 '{title}' (UID: {uid}) 已保存到 {file_path}")
        return file_path
    except requests.exceptions.RequestException as e:
        logging.error(f"获取或保存仪表盘 '{uid}' 失败: {e}")
        return None


def create_zip_archive(source_dir, output_zip_path):
    """将指定目录下的所有内容打包成ZIP文件。"""
    try:
        # shutil.make_archive(base_name, format, root_dir)
        # base_name: 归档文件的名称，不包含扩展名
        # format: 归档格式，例如 'zip', 'tar', 'gztar', 'bztar', 'xztar'
        # root_dir: 要归档的目录
        base_name = os.path.splitext(output_zip_path)[0]
        shutil.make_archive(base_name, 'zip', source_dir)
        logging.info(f"成功创建ZIP归档文件: {output_zip_path}")
        return True
    except Exception as e:
        logging.error(f"创建ZIP归档文件失败: {e}")
        return False


def upload_to_cos(cos_client, file_path):
    """上传文件到 COS。"""
    # 构造COS上的路径，使用文件名作为key，并添加前缀
    cos_key = f"grafana_backups/{os.path.basename(file_path)}"

    try:
        cos_client.upload_file(Bucket=COS_BUCKET,
                               LocalFilePath=file_path,
                               Key=cos_key,
                               EnableMD5=False)
        logging.info(f"已成功上传 {file_path} 到 COS 存储桶 {COS_BUCKET}，路径为 {cos_key}")
    except Exception as e:
        logging.error(f"上传文件 {file_path} 到 COS 失败: {e}")


def get_folder_path_map(api_client: GrafanaAPI) -> Dict[str, str]:
    """从扁平的文件夹列表递归构建嵌套路径映射。"""
    all_folders = api_client.list_folders()
    if not all_folders:
        logging.warning("未在Grafana中找到任何文件夹。")
        return {}

    # 按 parent_uid 对文件夹进行分组
    folders_by_parent = {}
    for folder in all_folders:
        parent_uid = folder.parent_uid
        if parent_uid not in folders_by_parent:
            folders_by_parent[parent_uid] = []
        folders_by_parent[parent_uid].append(folder)

    path_map = {}

    def _build_paths_recursive(parent_uid: Optional[str], current_path: List[str]):
        """递归构建路径"""
        if parent_uid not in folders_by_parent:
            return

        for folder in folders_by_parent[parent_uid]:
            # 清洗文件夹标题以用作路径
            clean_title = re.sub(r'[\\/:*?"<>|]', '_', folder.title)
            new_path = current_path + [clean_title]
            path_map[folder.uid] = os.path.join(*new_path)
            
            # 递归到子文件夹
            _build_paths_recursive(folder.uid, new_path)

    # 从根文件夹开始递归 (父UID为None的文件夹)
    logging.info("开始构建文件夹路径...")
    _build_paths_recursive(None, [])
    logging.info("文件夹路径构建完成。")

    return path_map


def get_all_dashboards(api_client: GrafanaAPI):
    """从 Grafana API 获取所有仪表盘的列表。"""
    return api_client.search_dashboards()


def main():
    """主执行函数"""
    logging.info("开始执行 Grafana 仪表盘备份任务...")

    # 获取 Grafana API Token，可以从环境变量或直接硬编码
    # 建议从环境变量获取，以提高安全性
    grafana_token = os.getenv(
        'GRAFANA_TOKEN', 'glsa_M5nAWArffjHCLAnKrszFJgRNnZaB4BBB_7ec2cb79')  # 替换为你的实际Token

    # 检查配置是否完整
    if not all([grafana_token, COS_SECRET_ID, COS_SECRET_KEY, COS_BUCKET]):
        logging.error("错误：脚本中的一个或多个密钥配置不完整。")
        return

    # 初始化 Grafana API 客户端
    api = GrafanaAPI(GRAFANA_URL, grafana_token)
    success, message = api.test_connection()
    if not success:
        logging.error(f"Grafana API 连接失败: {message}")
        return

    # 初始化 COS 客户端
    try:
        config = CosConfig(Region=COS_REGION,
                           SecretId=COS_SECRET_ID, SecretKey=COS_SECRET_KEY)
        cos_client = CosS3Client(config)
    except Exception as e:
        logging.error(f"初始化 COS 客户端失败: {e}")
        return

    # 创建带时间戳的备份目录
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    global BACKUP_DIR  # 声明为全局变量，以便其他函数使用
    BACKUP_DIR = os.path.join(os.path.dirname(
        __file__), '..', 'output', 'dashboard_backups', timestamp)
    os.makedirs(BACKUP_DIR, exist_ok=True)
    logging.info(f"本次备份目录: {BACKUP_DIR}")

    # 1. 获取文件夹路径映射
    folder_map = get_folder_path_map(api)
    if folder_map is None:
        logging.error("无法获取文件夹映射，任务终止。")
        return
    logging.info(f"成功构建文件夹路径映射: {len(folder_map)}个文件夹")

    # 2. 获取所有仪表盘
    all_dashboards = get_all_dashboards(api)
    if not all_dashboards:
        logging.warning("未在Grafana中找到任何仪表盘。")
        return

    successful_backups = 0
    for dash in all_dashboards:
        folder_uid = dash.folderUid
        # 从映射中获取完整路径，如果仪表盘在根目录(General)，则folder_uid为None
        relative_path = folder_map.get(folder_uid, '') if folder_uid else ''
        full_local_path = os.path.join(BACKUP_DIR, relative_path)

        # 保存到本地
        local_file_path = save_dashboard_locally(
            api_client=api, uid=dash.uid, full_local_path=full_local_path)

        # 统计成功备份的仪表盘数量
        if local_file_path:
            successful_backups += 1

    total_backups = successful_backups
    logging.info(f"仪表盘本地备份完成。共成功备份 {total_backups} 个仪表盘到 {BACKUP_DIR}。")

    # 打包备份目录
    zip_file_name = f"grafana_dashboards_backup_{timestamp}.zip"
    zip_file_path = os.path.join(os.path.dirname(BACKUP_DIR), zip_file_name)
    if create_zip_archive(BACKUP_DIR, zip_file_path):
        upload_to_cos(cos_client, zip_file_path)
    else:
        logging.error("打包备份文件失败，跳过COS上传。")

    logging.info("备份任务完成。")


if __name__ == '__main__':
    main()
